/**
 * 水下粒子系统
 * 用于实现水下气泡和悬浮物效果
 */
import * as THREE from 'three';
import { Debug } from '../../utils/Debug';
import type { Entity } from '../../core/Entity';
import { System } from '../../core/System';
import { WaterBodyComponent, WaterBodyType } from '../../physics/water/WaterBodyComponent';

/**
 * 粒子类型
 */
export enum UnderwaterParticleType {
  /** 气泡 */
  BUBBLE = 'bubble',
  /** 悬浮物 */
  DEBRIS = 'debris',
  /** 光束 */
  LIGHT_SHAFT = 'light_shaft',
  /** 鱼群 */
  FISH_SCHOOL = 'fish_school',
  /** 水草 */
  SEAWEED = 'seaweed',
  /** 浮游生物 */
  PLANKTON = 'plankton',
  /** 水流 */
  WATER_CURRENT = 'water_current',
  /** 沉淀物 */
  SEDIMENT = 'sediment',
  /** 水泡沫 */
  FOAM = 'foam',
  /** 水雾 */
  MIST = 'mist',
  /** 水花 */
  SPLASH = 'splash',
  /** 水滴 */
  DROPLET = 'droplet',
  /** 水波纹 */
  RIPPLE = 'ripple',
  /** 水藻 */
  ALGAE = 'algae'
}

/**
 * 粒子配置
 */
export interface UnderwaterParticleConfig {
  /** 粒子类型 */
  type: UnderwaterParticleType;
  /** 粒子数量 */
  count: number;
  /** 粒子大小 */
  size: number | [number, number];
  /** 粒子颜色 */
  color?: THREE.Color | number | string;
  /** 粒子透明度 */
  opacity?: number;
  /** 粒子生命周期 */
  lifetime?: number | [number, number];
  /** 粒子速度 */
  speed?: number | [number, number];
  /** 粒子加速度 */
  acceleration?: THREE.Vector3;
  /** 粒子旋转 */
  rotation?: boolean;
  /** 粒子旋转速度 */
  rotationSpeed?: number | [number, number];
  /** 粒子贴图 */
  texture?: THREE.Texture | string;
  /** 粒子混合模式 */
  blending?: THREE.Blending;
  /** 粒子发射区域 */
  emissionArea?: {
    /** 形状 */
    shape: 'box' | 'sphere' | 'cylinder';
    /** 尺寸 */
    size: THREE.Vector3 | number;
    /** 位置 */
    position?: THREE.Vector3;
  };
  /** 自定义着色器 */
  customShader?: {
    /** 顶点着色器 */
    vertex?: string;
    /** 片段着色器 */
    fragment?: string;
    /** 统一变量 */
    uniforms?: { [key: string]: THREE.IUniform };
  };
}

/**
 * 水下粒子系统配置
 */
export interface UnderwaterParticleSystemConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 最大粒子数量 */
  maxParticles?: number;
  /** 是否使用GPU加速 */
  useGPU?: boolean;
  /** 是否自动调整性能 */
  autoAdjustPerformance?: boolean;
  /** 是否启用调试 */
  debug?: boolean;
}

/**
 * 水下粒子系统
 */
export class UnderwaterParticleSystem extends System {
  /** 配置 */
  private config: UnderwaterParticleSystemConfig;
  /** 粒子组 */
  private particleGroups: Map<string, THREE.Points> = new Map();
  /** 粒子配置 */
  private particleConfigs: Map<string, UnderwaterParticleConfig> = new Map();
  /** 粒子几何体 */
  private particleGeometries: Map<string, THREE.BufferGeometry> = new Map();
  /** 粒子材质 */
  private particleMaterials: Map<string, THREE.Material> = new Map();
  /** 粒子属性 */
  private particleAttributes: Map<string, {
    /** 位置 */
    positions: Float32Array;
    /** 大小 */
    sizes: Float32Array;
    /** 颜色 */
    colors: Float32Array;
    /** 透明度 */
    opacities: Float32Array;
    /** 生命周期 */
    lifetimes: Float32Array;
    /** 最大生命周期 */
    maxLifetimes: Float32Array;
    /** 速度 */
    velocities: Float32Array;
    /** 旋转 */
    rotations: Float32Array;
    /** 旋转速度 */
    rotationSpeeds: Float32Array;
  }> = new Map();
  /** 场景 */
  private scene: THREE.Scene | null = null;
  /** 相机 */
  private camera: THREE.Camera | null = null;
  /** 水体组件 */
  private waterBodies: Map<string, WaterBodyComponent> = new Map();
  /** 是否初始化 */
  private initialized: boolean = false;
  /** 时间 */
  private time: number = 0;
  /** 性能监视器 */
  private performanceMonitor: {
    /** 帧率 */
    fps: number;
    /** 上一帧时间 */
    lastFrameTime: number;
    /** 粒子数量 */
    particleCount: number;
    /** 渲染时间 */
    renderTime: number;
  } = {
    fps: 0,
    lastFrameTime: 0,
    particleCount: 0,
    renderTime: 0
  };

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: UnderwaterParticleSystemConfig = {}) {
    super();
    this.config = {
      enabled: config.enabled !== undefined ? config.enabled : true,
      maxParticles: config.maxParticles || 10000,
      useGPU: config.useGPU !== undefined ? config.useGPU : true,
      autoAdjustPerformance: config.autoAdjustPerformance !== undefined ? config.autoAdjustPerformance : true,
      debug: config.debug !== undefined ? config.debug : false
    };
  }

  /**
   * 初始化
   * @param scene 场景
   * @param camera 相机
   */
  public initialize(scene: THREE.Scene, camera: THREE.Camera): void {
    this.scene = scene;
    this.camera = camera;
    this.initialized = true;

    if (this.config.debug) {
      Debug.log('UnderwaterParticleSystem', '初始化水下粒子系统');
    }
  }

  /**
   * 添加水体
   * @param entity 实体
   * @param waterBody 水体组件
   */
  public addWaterBody(entity: Entity, waterBody: WaterBodyComponent): void {
    this.waterBodies.set(entity.id, waterBody);

    // 根据水体类型创建默认粒子
    this.createDefaultParticlesForWaterBody(entity.id, waterBody);

    if (this.config.debug) {
      Debug.log('UnderwaterParticleSystem', `添加水体: ${entity.id}`);
    }
  }

  /**
   * 移除水体
   * @param entityId 实体ID
   */
  public removeWaterBody(entityId: string): void {
    this.waterBodies.delete(entityId);

    // 移除相关粒子
    this.removeParticlesForWaterBody(entityId);

    if (this.config.debug) {
      Debug.log('UnderwaterParticleSystem', `移除水体: ${entityId}`);
    }
  }

  /**
   * 为水体创建默认粒子
   * @param entityId 实体ID
   * @param waterBody 水体组件
   */
  private createDefaultParticlesForWaterBody(entityId: string, waterBody: WaterBodyComponent): void {
    // 根据水体类型创建不同的默认粒子
    switch (waterBody.getWaterType()) {
      case WaterBodyType.OCEAN:
        this.createOceanParticles(entityId, waterBody);
        break;

      case WaterBodyType.LAKE:
        this.createLakeParticles(entityId, waterBody);
        break;

      case WaterBodyType.RIVER:
        this.createRiverParticles(entityId, waterBody);
        break;

      case WaterBodyType.UNDERGROUND_LAKE:
        this.createUndergroundLakeParticles(entityId, waterBody);
        break;

      case WaterBodyType.UNDERGROUND_RIVER:
        this.createUndergroundRiverParticles(entityId, waterBody);
        break;

      case WaterBodyType.HOT_SPRING:
        this.createHotSpringParticles(entityId, waterBody);
        break;

      default:
        this.createGenericWaterParticles(entityId, waterBody);
        break;
    }
  }

  /**
   * 创建海洋粒子
   * @param entityId 实体ID
   * @param waterBody 水体组件
   */
  private createOceanParticles(entityId: string, waterBody: WaterBodyComponent): void {
    // 创建气泡粒子
    this.addParticleGroup(entityId, 'bubbles', {
      type: UnderwaterParticleType.BUBBLE,
      count: 500,
      size: [0.05, 0.2],
      color: 0xffffff,
      opacity: 0.6,
      lifetime: [2, 5],
      speed: [0.2, 0.5],
      acceleration: new THREE.Vector3(0, 0.1, 0),
      rotation: true,
      rotationSpeed: [0.1, 0.3],
      blending: THREE.AdditiveBlending,
      emissionArea: {
        shape: 'box',
        size: new THREE.Vector3(waterBody.getSize().width, waterBody.getSize().height / 2, waterBody.getSize().depth),
        position: new THREE.Vector3(0, -waterBody.getSize().height / 4, 0)
      }
    });

    // 创建悬浮物粒子
    this.addParticleGroup(entityId, 'debris', {
      type: UnderwaterParticleType.DEBRIS,
      count: 300,
      size: [0.1, 0.3],
      color: 0xcccccc,
      opacity: 0.4,
      lifetime: [5, 10],
      speed: [0.05, 0.1],
      rotation: true,
      rotationSpeed: [0.05, 0.1],
      emissionArea: {
        shape: 'box',
        size: new THREE.Vector3(waterBody.getSize().width, waterBody.getSize().height / 2, waterBody.getSize().depth),
        position: new THREE.Vector3(0, -waterBody.getSize().height / 4, 0)
      }
    });

    // 创建光束粒子
    this.addParticleGroup(entityId, 'lightShafts', {
      type: UnderwaterParticleType.LIGHT_SHAFT,
      count: 50,
      size: [0.5, 2],
      color: 0xffffcc,
      opacity: 0.3,
      lifetime: [8, 15],
      blending: THREE.AdditiveBlending,
      emissionArea: {
        shape: 'box',
        size: new THREE.Vector3(waterBody.getSize().width, 0.1, waterBody.getSize().depth),
        position: new THREE.Vector3(0, waterBody.getSize().height / 2 - 0.05, 0)
      }
    });
  }

  /**
   * 创建湖泊粒子
   * @param entityId 实体ID
   * @param waterBody 水体组件
   */
  private createLakeParticles(entityId: string, waterBody: WaterBodyComponent): void {
    // 创建气泡粒子（较少）
    this.addParticleGroup(entityId, 'bubbles', {
      type: UnderwaterParticleType.BUBBLE,
      count: 150,
      size: [0.05, 0.15],
      color: 0xffffff,
      opacity: 0.5,
      lifetime: [5, 12],
      speed: [0.05, 0.15],
      acceleration: new THREE.Vector3(0, 0.05, 0),
      rotation: true,
      rotationSpeed: [0.05, 0.1],
      blending: THREE.AdditiveBlending,
      emissionArea: {
        shape: 'box',
        size: new THREE.Vector3(waterBody.getSize().width * 0.8, waterBody.getSize().height / 3, waterBody.getSize().depth * 0.8),
        position: new THREE.Vector3(0, -waterBody.getSize().height / 4, 0)
      }
    });

    // 创建悬浮物粒子
    this.addParticleGroup(entityId, 'debris', {
      type: UnderwaterParticleType.DEBRIS,
      count: 200,
      size: [0.1, 0.3],
      color: 0x88aa99,
      opacity: 0.3,
      lifetime: [8, 15],
      speed: [0.02, 0.05],
      rotation: true,
      rotationSpeed: [0.02, 0.05],
      emissionArea: {
        shape: 'box',
        size: new THREE.Vector3(waterBody.getSize().width, waterBody.getSize().height / 2, waterBody.getSize().depth),
        position: new THREE.Vector3(0, -waterBody.getSize().height / 4, 0)
      }
    });

    // 创建光束粒子
    this.addParticleGroup(entityId, 'lightShafts', {
      type: UnderwaterParticleType.LIGHT_SHAFT,
      count: 40,
      size: [0.5, 2],
      color: 0xffffdd,
      opacity: 0.25,
      lifetime: [10, 20],
      blending: THREE.AdditiveBlending,
      emissionArea: {
        shape: 'box',
        size: new THREE.Vector3(waterBody.getSize().width * 0.8, 0.1, waterBody.getSize().depth * 0.8),
        position: new THREE.Vector3(0, waterBody.getSize().height / 2 - 0.05, 0)
      }
    });

    // 创建水草粒子
    this.addParticleGroup(entityId, 'seaweed', {
      type: UnderwaterParticleType.SEAWEED,
      count: 80,
      size: [0.5, 1.5],
      color: 0x336644,
      opacity: 0.6,
      lifetime: [30, 60],
      speed: [0.01, 0.02],
      rotation: true,
      rotationSpeed: [0.01, 0.02],
      emissionArea: {
        shape: 'box',
        size: new THREE.Vector3(waterBody.getSize().width * 0.9, 0.1, waterBody.getSize().depth * 0.9),
        position: new THREE.Vector3(0, -waterBody.getSize().height / 2 + 0.5, 0)
      }
    });

    // 创建鱼群粒子
    this.addParticleGroup(entityId, 'fishSchool', {
      type: UnderwaterParticleType.FISH_SCHOOL,
      count: 30,
      size: [0.2, 0.4],
      color: 0x88aacc,
      opacity: 0.7,
      lifetime: [15, 30],
      speed: [0.1, 0.2],
      rotation: true,
      rotationSpeed: [0.1, 0.2],
      emissionArea: {
        shape: 'box',
        size: new THREE.Vector3(waterBody.getSize().width * 0.7, waterBody.getSize().height * 0.6, waterBody.getSize().depth * 0.7),
        position: new THREE.Vector3(0, -waterBody.getSize().height * 0.1, 0)
      }
    });
  }

  /**
   * 创建河流粒子
   * @param entityId 实体ID
   * @param waterBody 水体组件
   */
  private createRiverParticles(entityId: string, waterBody: WaterBodyComponent): void {
    // 获取流向
    const flowDirection = waterBody.getFlowDirection();
    const flowSpeed = waterBody.getFlowSpeed();

    // 创建气泡粒子（沿流向移动）
    this.addParticleGroup(entityId, 'bubbles', {
      type: UnderwaterParticleType.BUBBLE,
      count: 200,
      size: [0.05, 0.15],
      color: 0xffffff,
      opacity: 0.6,
      lifetime: [2, 5],
      speed: [0.1, 0.3],
      acceleration: new THREE.Vector3(
        flowDirection.x * flowSpeed * 0.2,
        0.1,
        flowDirection.z * flowSpeed * 0.2
      ),
      rotation: true,
      rotationSpeed: [0.1, 0.3],
      blending: THREE.AdditiveBlending,
      emissionArea: {
        shape: 'box',
        size: new THREE.Vector3(waterBody.getSize().width * 0.9, waterBody.getSize().height / 2, waterBody.getSize().depth * 0.9),
        position: new THREE.Vector3(
          -flowDirection.x * waterBody.getSize().width * 0.4,
          -waterBody.getSize().height / 4,
          -flowDirection.z * waterBody.getSize().depth * 0.4
        )
      }
    });

    // 创建水流粒子（沿流向快速移动）
    this.addParticleGroup(entityId, 'waterCurrent', {
      type: UnderwaterParticleType.WATER_CURRENT,
      count: 150,
      size: [0.1, 0.3],
      color: 0x88aacc,
      opacity: 0.3,
      lifetime: [1, 3],
      speed: [0.2, 0.5],
      acceleration: new THREE.Vector3(
        flowDirection.x * flowSpeed * 0.5,
        0,
        flowDirection.z * flowSpeed * 0.5
      ),
      rotation: true,
      rotationSpeed: [0.2, 0.4],
      emissionArea: {
        shape: 'box',
        size: new THREE.Vector3(waterBody.getSize().width * 0.7, waterBody.getSize().height * 0.7, waterBody.getSize().depth * 0.7),
        position: new THREE.Vector3(
          -flowDirection.x * waterBody.getSize().width * 0.4,
          0,
          -flowDirection.z * waterBody.getSize().depth * 0.4
        )
      }
    });

    // 创建悬浮物粒子（沿流向缓慢移动）
    this.addParticleGroup(entityId, 'debris', {
      type: UnderwaterParticleType.DEBRIS,
      count: 180,
      size: [0.1, 0.2],
      color: 0x778866,
      opacity: 0.4,
      lifetime: [3, 8],
      speed: [0.05, 0.1],
      acceleration: new THREE.Vector3(
        flowDirection.x * flowSpeed * 0.3,
        0,
        flowDirection.z * flowSpeed * 0.3
      ),
      rotation: true,
      rotationSpeed: [0.05, 0.1],
      emissionArea: {
        shape: 'box',
        size: new THREE.Vector3(waterBody.getSize().width, waterBody.getSize().height * 0.8, waterBody.getSize().depth),
        position: new THREE.Vector3(
          -flowDirection.x * waterBody.getSize().width * 0.4,
          -waterBody.getSize().height * 0.1,
          -flowDirection.z * waterBody.getSize().depth * 0.4
        )
      }
    });

    // 创建光束粒子
    this.addParticleGroup(entityId, 'lightShafts', {
      type: UnderwaterParticleType.LIGHT_SHAFT,
      count: 30,
      size: [0.3, 1],
      color: 0xffffdd,
      opacity: 0.2,
      lifetime: [5, 10],
      blending: THREE.AdditiveBlending,
      emissionArea: {
        shape: 'box',
        size: new THREE.Vector3(waterBody.getSize().width * 0.8, 0.1, waterBody.getSize().depth * 0.8),
        position: new THREE.Vector3(0, waterBody.getSize().height / 2 - 0.05, 0)
      }
    });
  }

  /**
   * 创建地下湖泊粒子
   * @param entityId 实体ID
   * @param waterBody 水体组件
   */
  private createUndergroundLakeParticles(entityId: string, waterBody: WaterBodyComponent): void {
    // 创建气泡粒子（较少）
    this.addParticleGroup(entityId, 'bubbles', {
      type: UnderwaterParticleType.BUBBLE,
      count: 200,
      size: [0.03, 0.1],
      color: 0xaaaaff,
      opacity: 0.4,
      lifetime: [5, 15],
      speed: [0.05, 0.1],
      acceleration: new THREE.Vector3(0, 0.05, 0),
      rotation: true,
      rotationSpeed: [0.05, 0.1],
      blending: THREE.AdditiveBlending,
      emissionArea: {
        shape: 'box',
        size: new THREE.Vector3(waterBody.getSize().width * 0.8, waterBody.getSize().height / 3, waterBody.getSize().depth * 0.8),
        position: new THREE.Vector3(0, -waterBody.getSize().height / 4, 0)
      }
    });

    // 创建悬浮物粒子（较多，较暗）
    this.addParticleGroup(entityId, 'debris', {
      type: UnderwaterParticleType.DEBRIS,
      count: 400,
      size: [0.05, 0.2],
      color: 0x334455,
      opacity: 0.3,
      lifetime: [8, 20],
      speed: [0.02, 0.05],
      rotation: true,
      rotationSpeed: [0.02, 0.05],
      emissionArea: {
        shape: 'box',
        size: new THREE.Vector3(waterBody.getSize().width, waterBody.getSize().height / 2, waterBody.getSize().depth),
        position: new THREE.Vector3(0, -waterBody.getSize().height / 4, 0)
      }
    });

    // 创建沉淀物粒子（缓慢下沉）
    this.addParticleGroup(entityId, 'sediment', {
      type: UnderwaterParticleType.SEDIMENT,
      count: 300,
      size: [0.1, 0.3],
      color: 0x223344,
      opacity: 0.2,
      lifetime: [10, 30],
      speed: [0.01, 0.03],
      acceleration: new THREE.Vector3(0, -0.01, 0),
      rotation: true,
      rotationSpeed: [0.01, 0.03],
      emissionArea: {
        shape: 'box',
        size: new THREE.Vector3(waterBody.getSize().width, waterBody.getSize().height * 0.1, waterBody.getSize().depth),
        position: new THREE.Vector3(0, waterBody.getSize().height / 3, 0)
      }
    });

    // 创建光束粒子（较弱，较少）
    this.addParticleGroup(entityId, 'lightShafts', {
      type: UnderwaterParticleType.LIGHT_SHAFT,
      count: 20,
      size: [0.5, 2],
      color: 0x223366,
      opacity: 0.2,
      lifetime: [15, 30],
      blending: THREE.AdditiveBlending,
      emissionArea: {
        shape: 'box',
        size: new THREE.Vector3(waterBody.getSize().width * 0.5, 0.1, waterBody.getSize().depth * 0.5),
        position: new THREE.Vector3(0, waterBody.getSize().height / 2 - 0.05, 0)
      }
    });

    // 创建水藻粒子（底部）
    this.addParticleGroup(entityId, 'algae', {
      type: UnderwaterParticleType.ALGAE,
      count: 50,
      size: [0.5, 1.5],
      color: 0x225544,
      opacity: 0.5,
      lifetime: [30, 60],
      speed: [0.01, 0.02],
      rotation: true,
      rotationSpeed: [0.01, 0.02],
      emissionArea: {
        shape: 'box',
        size: new THREE.Vector3(waterBody.getSize().width * 0.8, 0.1, waterBody.getSize().depth * 0.8),
        position: new THREE.Vector3(0, -waterBody.getSize().height / 2 + 0.5, 0)
      }
    });
  }

  /**
   * 创建地下河流粒子
   * @param entityId 实体ID
   * @param waterBody 水体组件
   */
  private createUndergroundRiverParticles(entityId: string, waterBody: WaterBodyComponent): void {
    // 获取流向
    const flowDirection = waterBody.getFlowDirection();
    const flowSpeed = waterBody.getFlowSpeed();

    // 创建气泡粒子（沿流向移动）
    this.addParticleGroup(entityId, 'bubbles', {
      type: UnderwaterParticleType.BUBBLE,
      count: 300,
      size: [0.03, 0.1],
      color: 0x99aaff,
      opacity: 0.4,
      lifetime: [3, 8],
      speed: [0.1, 0.2],
      acceleration: new THREE.Vector3(
        flowDirection.x * flowSpeed * 0.1,
        0.08,
        flowDirection.z * flowSpeed * 0.1
      ),
      rotation: true,
      rotationSpeed: [0.1, 0.2],
      blending: THREE.AdditiveBlending,
      emissionArea: {
        shape: 'box',
        size: new THREE.Vector3(waterBody.getSize().width * 0.9, waterBody.getSize().height / 3, waterBody.getSize().depth * 0.9),
        position: new THREE.Vector3(
          -flowDirection.x * waterBody.getSize().width * 0.4,
          -waterBody.getSize().height / 4,
          -flowDirection.z * waterBody.getSize().depth * 0.4
        )
      }
    });

    // 创建水流粒子（沿流向快速移动）
    this.addParticleGroup(entityId, 'waterCurrent', {
      type: UnderwaterParticleType.WATER_CURRENT,
      count: 200,
      size: [0.1, 0.3],
      color: 0x334466,
      opacity: 0.2,
      lifetime: [1, 3],
      speed: [0.2, 0.5],
      acceleration: new THREE.Vector3(
        flowDirection.x * flowSpeed * 0.5,
        0,
        flowDirection.z * flowSpeed * 0.5
      ),
      rotation: true,
      rotationSpeed: [0.2, 0.4],
      emissionArea: {
        shape: 'box',
        size: new THREE.Vector3(waterBody.getSize().width * 0.5, waterBody.getSize().height * 0.5, waterBody.getSize().depth * 0.5),
        position: new THREE.Vector3(
          -flowDirection.x * waterBody.getSize().width * 0.4,
          0,
          -flowDirection.z * waterBody.getSize().depth * 0.4
        )
      }
    });

    // 创建悬浮物粒子（沿流向缓慢移动）
    this.addParticleGroup(entityId, 'debris', {
      type: UnderwaterParticleType.DEBRIS,
      count: 250,
      size: [0.05, 0.15],
      color: 0x223344,
      opacity: 0.3,
      lifetime: [5, 10],
      speed: [0.05, 0.1],
      acceleration: new THREE.Vector3(
        flowDirection.x * flowSpeed * 0.2,
        0,
        flowDirection.z * flowSpeed * 0.2
      ),
      rotation: true,
      rotationSpeed: [0.05, 0.1],
      emissionArea: {
        shape: 'box',
        size: new THREE.Vector3(waterBody.getSize().width * 0.8, waterBody.getSize().height * 0.6, waterBody.getSize().depth * 0.8),
        position: new THREE.Vector3(
          -flowDirection.x * waterBody.getSize().width * 0.4,
          -waterBody.getSize().height * 0.2,
          -flowDirection.z * waterBody.getSize().depth * 0.4
        )
      }
    });

    // 创建水雾粒子（水面附近）
    this.addParticleGroup(entityId, 'mist', {
      type: UnderwaterParticleType.MIST,
      count: 100,
      size: [0.3, 0.8],
      color: 0x334455,
      opacity: 0.15,
      lifetime: [3, 8],
      speed: [0.05, 0.1],
      acceleration: new THREE.Vector3(
        flowDirection.x * flowSpeed * 0.1,
        0.01,
        flowDirection.z * flowSpeed * 0.1
      ),
      blending: THREE.AdditiveBlending,
      emissionArea: {
        shape: 'box',
        size: new THREE.Vector3(waterBody.getSize().width * 0.9, waterBody.getSize().height * 0.1, waterBody.getSize().depth * 0.9),
        position: new THREE.Vector3(0, waterBody.getSize().height * 0.4, 0)
      }
    });
  }

  /**
   * 创建温泉粒子
   * @param entityId 实体ID
   * @param waterBody 水体组件
   */
  private createHotSpringParticles(entityId: string, waterBody: WaterBodyComponent): void {
    // 创建大量气泡粒子（快速上升）
    this.addParticleGroup(entityId, 'bubbles', {
      type: UnderwaterParticleType.BUBBLE,
      count: 500,
      size: [0.05, 0.2],
      color: 0xffffff,
      opacity: 0.7,
      lifetime: [1, 3],
      speed: [0.3, 0.8],
      acceleration: new THREE.Vector3(0, 0.2, 0),
      rotation: true,
      rotationSpeed: [0.2, 0.5],
      blending: THREE.AdditiveBlending,
      emissionArea: {
        shape: 'box',
        size: new THREE.Vector3(waterBody.getSize().width * 0.8, 0.1, waterBody.getSize().depth * 0.8),
        position: new THREE.Vector3(0, -waterBody.getSize().height / 2 + 0.1, 0)
      }
    });

    // 创建水雾粒子（水面）
    this.addParticleGroup(entityId, 'mist', {
      type: UnderwaterParticleType.MIST,
      count: 200,
      size: [0.5, 1.5],
      color: 0xddddff,
      opacity: 0.3,
      lifetime: [2, 5],
      speed: [0.1, 0.2],
      acceleration: new THREE.Vector3(0, 0.05, 0),
      blending: THREE.AdditiveBlending,
      emissionArea: {
        shape: 'box',
        size: new THREE.Vector3(waterBody.getSize().width, 0.1, waterBody.getSize().depth),
        position: new THREE.Vector3(0, waterBody.getSize().height / 2 - 0.05, 0)
      }
    });

    // 创建水流粒子（底部向上）
    this.addParticleGroup(entityId, 'waterCurrent', {
      type: UnderwaterParticleType.WATER_CURRENT,
      count: 150,
      size: [0.2, 0.5],
      color: 0xaabbcc,
      opacity: 0.2,
      lifetime: [2, 4],
      speed: [0.2, 0.4],
      acceleration: new THREE.Vector3(0, 0.1, 0),
      rotation: true,
      rotationSpeed: [0.1, 0.3],
      emissionArea: {
        shape: 'box',
        size: new THREE.Vector3(waterBody.getSize().width * 0.6, 0.1, waterBody.getSize().depth * 0.6),
        position: new THREE.Vector3(0, -waterBody.getSize().height / 2 + 0.1, 0)
      }
    });

    // 创建矿物粒子（悬浮）
    this.addParticleGroup(entityId, 'minerals', {
      type: UnderwaterParticleType.SEDIMENT,
      count: 300,
      size: [0.02, 0.08],
      color: 0xccddee,
      opacity: 0.5,
      lifetime: [5, 15],
      speed: [0.05, 0.1],
      acceleration: new THREE.Vector3(0, 0.01, 0),
      rotation: true,
      rotationSpeed: [0.05, 0.1],
      emissionArea: {
        shape: 'box',
        size: new THREE.Vector3(waterBody.getSize().width, waterBody.getSize().height, waterBody.getSize().depth),
        position: new THREE.Vector3(0, 0, 0)
      }
    });

    // 创建水泡沫粒子（水面）
    this.addParticleGroup(entityId, 'foam', {
      type: UnderwaterParticleType.FOAM,
      count: 100,
      size: [0.1, 0.3],
      color: 0xffffff,
      opacity: 0.6,
      lifetime: [3, 8],
      speed: [0.02, 0.05],
      emissionArea: {
        shape: 'box',
        size: new THREE.Vector3(waterBody.getSize().width * 0.9, 0.1, waterBody.getSize().depth * 0.9),
        position: new THREE.Vector3(0, waterBody.getSize().height / 2 - 0.05, 0)
      }
    });
  }

  /**
   * 创建通用水体粒子
   * @param entityId 实体ID
   * @param waterBody 水体组件
   */
  private createGenericWaterParticles(entityId: string, waterBody: WaterBodyComponent): void {
    // 创建气泡粒子
    this.addParticleGroup(entityId, 'bubbles', {
      type: UnderwaterParticleType.BUBBLE,
      count: 200,
      size: [0.05, 0.15],
      color: 0xffffff,
      opacity: 0.5,
      lifetime: [3, 8],
      speed: [0.1, 0.3],
      acceleration: new THREE.Vector3(0, 0.1, 0),
      rotation: true,
      rotationSpeed: [0.1, 0.2],
      blending: THREE.AdditiveBlending,
      emissionArea: {
        shape: 'box',
        size: new THREE.Vector3(waterBody.getSize().width * 0.8, waterBody.getSize().height / 2, waterBody.getSize().depth * 0.8),
        position: new THREE.Vector3(0, -waterBody.getSize().height / 4, 0)
      }
    });

    // 创建悬浮物粒子
    this.addParticleGroup(entityId, 'debris', {
      type: UnderwaterParticleType.DEBRIS,
      count: 150,
      size: [0.1, 0.2],
      color: 0xcccccc,
      opacity: 0.3,
      lifetime: [5, 12],
      speed: [0.03, 0.08],
      rotation: true,
      rotationSpeed: [0.03, 0.08],
      emissionArea: {
        shape: 'box',
        size: new THREE.Vector3(waterBody.getSize().width, waterBody.getSize().height / 2, waterBody.getSize().depth),
        position: new THREE.Vector3(0, -waterBody.getSize().height / 4, 0)
      }
    });

    // 创建浮游生物粒子
    this.addParticleGroup(entityId, 'plankton', {
      type: UnderwaterParticleType.PLANKTON,
      count: 100,
      size: [0.02, 0.05],
      color: 0xaaddff,
      opacity: 0.4,
      lifetime: [8, 15],
      speed: [0.02, 0.05],
      rotation: true,
      rotationSpeed: [0.02, 0.05],
      emissionArea: {
        shape: 'box',
        size: new THREE.Vector3(waterBody.getSize().width * 0.9, waterBody.getSize().height * 0.7, waterBody.getSize().depth * 0.9),
        position: new THREE.Vector3(0, -waterBody.getSize().height * 0.1, 0)
      }
    });

    // 创建光束粒子
    this.addParticleGroup(entityId, 'lightShafts', {
      type: UnderwaterParticleType.LIGHT_SHAFT,
      count: 30,
      size: [0.5, 1.5],
      color: 0xffffcc,
      opacity: 0.2,
      lifetime: [10, 20],
      blending: THREE.AdditiveBlending,
      emissionArea: {
        shape: 'box',
        size: new THREE.Vector3(waterBody.getSize().width * 0.7, 0.1, waterBody.getSize().depth * 0.7),
        position: new THREE.Vector3(0, waterBody.getSize().height / 2 - 0.05, 0)
      }
    });
  }

  /**
   * 添加粒子组
   * @param entityId 实体ID
   * @param groupId 组ID
   * @param config 粒子配置
   */
  public addParticleGroup(entityId: string, groupId: string, config: UnderwaterParticleConfig): void {
    if (!this.initialized || !this.scene) {
      Debug.warn('UnderwaterParticleSystem', '粒子系统未初始化');
      return;
    }

    const particleId = `${entityId}_${groupId}`;

    // 检查是否已存在
    if (this.particleGroups.has(particleId)) {
      this.removeParticleGroup(entityId, groupId);
    }

    // 保存配置
    this.particleConfigs.set(particleId, config);

    // 创建几何体
    const geometry = new THREE.BufferGeometry();
    this.particleGeometries.set(particleId, geometry);

    // 创建材质
    let material: THREE.Material;
    if (config.customShader) {
      // 使用自定义着色器
      material = new THREE.ShaderMaterial({
        uniforms: config.customShader.uniforms || {},
        vertexShader: config.customShader.vertex || '',
        fragmentShader: config.customShader.fragment || '',
        transparent: true,
        blending: config.blending || THREE.NormalBlending,
        depthWrite: false
      });
    } else {
      // 使用标准点材质
      material = new THREE.PointsMaterial({
        color: config.color !== undefined ? new THREE.Color(config.color) : 0xffffff,
        size: typeof config.size === 'number' ? config.size : config.size[0],
        transparent: true,
        opacity: config.opacity !== undefined ? config.opacity : 1.0,
        blending: config.blending || THREE.NormalBlending,
        depthWrite: false,
        vertexColors: true
      });

      // 设置贴图
      if (config.texture) {
        if (typeof config.texture === 'string') {
          const textureLoader = new THREE.TextureLoader();
          textureLoader.load(config.texture, (texture) => {
            (material as THREE.PointsMaterial).map = texture;
            material.needsUpdate = true;
          });
        } else {
          (material as THREE.PointsMaterial).map = config.texture;
        }
      }
    }
    this.particleMaterials.set(particleId, material);

    // 创建粒子属性
    const count = config.count;
    const positions = new Float32Array(count * 3);
    const sizes = new Float32Array(count);
    const colors = new Float32Array(count * 3);
    const opacities = new Float32Array(count);
    const lifetimes = new Float32Array(count);
    const maxLifetimes = new Float32Array(count);
    const velocities = new Float32Array(count * 3);
    const rotations = new Float32Array(count);
    const rotationSpeeds = new Float32Array(count);

    // 初始化粒子属性
    this.initializeParticleAttributes(
      particleId,
      config,
      positions,
      sizes,
      colors,
      opacities,
      lifetimes,
      maxLifetimes,
      velocities,
      rotations,
      rotationSpeeds
    );

    // 设置几何体属性
    geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
    geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));
    geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
    geometry.setAttribute('opacity', new THREE.BufferAttribute(opacities, 1));

    // 创建粒子系统
    const particles = new THREE.Points(geometry, material);
    particles.name = particleId;
    particles.frustumCulled = false;
    this.particleGroups.set(particleId, particles);

    // 保存粒子属性
    this.particleAttributes.set(particleId, {
      positions,
      sizes,
      colors,
      opacities,
      lifetimes,
      maxLifetimes,
      velocities,
      rotations,
      rotationSpeeds
    });

    // 添加到场景
    this.scene.add(particles);

    // 更新性能监视器
    this.performanceMonitor.particleCount += count;

    if (this.config.debug) {
      Debug.log('UnderwaterParticleSystem', `添加粒子组: ${particleId}, 数量: ${count}`);
    }
  }

  /**
   * 初始化粒子属性
   */
  private initializeParticleAttributes(
    particleId: string,
    config: UnderwaterParticleConfig,
    positions: Float32Array,
    sizes: Float32Array,
    colors: Float32Array,
    opacities: Float32Array,
    lifetimes: Float32Array,
    maxLifetimes: Float32Array,
    velocities: Float32Array,
    rotations: Float32Array,
    rotationSpeeds: Float32Array
  ): void {
    const count = config.count;
    const color = config.color !== undefined ? new THREE.Color(config.color) : new THREE.Color(0xffffff);
    const waterBody = this.getWaterBodyFromParticleId(particleId);

    // 发射区域
    const emissionArea = config.emissionArea || {
      shape: 'box' as const,
      size: waterBody ? new THREE.Vector3(
        waterBody.getSize().width,
        waterBody.getSize().height,
        waterBody.getSize().depth
      ) : new THREE.Vector3(10, 10, 10),
      position: new THREE.Vector3(0, 0, 0)
    };

    for (let i = 0; i < count; i++) {
      // 位置
      const position = this.generateRandomPositionInArea(emissionArea);
      positions[i * 3] = position.x;
      positions[i * 3 + 1] = position.y;
      positions[i * 3 + 2] = position.z;

      // 大小
      sizes[i] = typeof config.size === 'number'
        ? config.size
        : Math.random() * (config.size[1] - config.size[0]) + config.size[0];

      // 颜色
      colors[i * 3] = color.r;
      colors[i * 3 + 1] = color.g;
      colors[i * 3 + 2] = color.b;

      // 透明度
      opacities[i] = config.opacity !== undefined ? config.opacity : 1.0;

      // 生命周期
      maxLifetimes[i] = typeof config.lifetime === 'number'
        ? config.lifetime
        : Math.random() * ((config.lifetime?.[1] || 5) - (config.lifetime?.[0] || 1)) + (config.lifetime?.[0] || 1);
      lifetimes[i] = Math.random() * maxLifetimes[i];

      // 速度
      const speed = typeof config.speed === 'number'
        ? config.speed
        : Math.random() * ((config.speed?.[1] || 1) - (config.speed?.[0] || 0)) + (config.speed?.[0] || 0);
      const direction = new THREE.Vector3(
        Math.random() * 2 - 1,
        Math.random() * 2 - 1,
        Math.random() * 2 - 1
      ).normalize();
      velocities[i * 3] = direction.x * speed;
      velocities[i * 3 + 1] = direction.y * speed;
      velocities[i * 3 + 2] = direction.z * speed;

      // 旋转
      rotations[i] = Math.random() * Math.PI * 2;
      rotationSpeeds[i] = typeof config.rotationSpeed === 'number'
        ? config.rotationSpeed
        : Math.random() * ((config.rotationSpeed?.[1] || 0.1) - (config.rotationSpeed?.[0] || 0)) + (config.rotationSpeed?.[0] || 0);
    }
  }

  /**
   * 在指定区域生成随机位置
   */
  private generateRandomPositionInArea(area: UnderwaterParticleConfig['emissionArea']): THREE.Vector3 {
    if (!area) {
      return new THREE.Vector3(
        Math.random() * 10 - 5,
        Math.random() * 10 - 5,
        Math.random() * 10 - 5
      );
    }

    const position = new THREE.Vector3();
    const areaPosition = area.position || new THREE.Vector3(0, 0, 0);

    switch (area.shape) {
      case 'box':
        const size = area.size instanceof THREE.Vector3
          ? area.size
          : new THREE.Vector3(area.size, area.size, area.size);
        position.set(
          (Math.random() - 0.5) * size.x + areaPosition.x,
          (Math.random() - 0.5) * size.y + areaPosition.y,
          (Math.random() - 0.5) * size.z + areaPosition.z
        );
        break;

      case 'sphere':
        const radius = area.size instanceof THREE.Vector3 ? area.size.x : area.size;
        const phi = Math.random() * Math.PI * 2;
        const theta = Math.random() * Math.PI;
        const r = radius * Math.cbrt(Math.random()); // 均匀分布在球体内
        position.set(
          r * Math.sin(theta) * Math.cos(phi) + areaPosition.x,
          r * Math.sin(theta) * Math.sin(phi) + areaPosition.y,
          r * Math.cos(theta) + areaPosition.z
        );
        break;

      case 'cylinder':
        const cylinderRadius = area.size instanceof THREE.Vector3 ? area.size.x : area.size;
        const height = area.size instanceof THREE.Vector3 ? area.size.y : area.size * 2;
        const angle = Math.random() * Math.PI * 2;
        const radiusValue = cylinderRadius * Math.sqrt(Math.random()); // 均匀分布在圆内
        position.set(
          radiusValue * Math.cos(angle) + areaPosition.x,
          (Math.random() - 0.5) * height + areaPosition.y,
          radiusValue * Math.sin(angle) + areaPosition.z
        );
        break;
    }

    return position;
  }

  /**
   * 从粒子ID获取水体组件
   */
  private getWaterBodyFromParticleId(particleId: string): WaterBodyComponent | null {
    const entityId = particleId.split('_')[0];
    return this.waterBodies.get(entityId) || null;
  }

  /**
   * 移除粒子组
   * @param entityId 实体ID
   * @param groupId 组ID
   */
  public removeParticleGroup(entityId: string, groupId: string): void {
    const particleId = `${entityId}_${groupId}`;

    // 从场景中移除
    const particles = this.particleGroups.get(particleId);
    if (particles && this.scene) {
      this.scene.remove(particles);
    }

    // 释放资源
    const geometry = this.particleGeometries.get(particleId);
    if (geometry) {
      (geometry as any).dispose();
    }

    const material = this.particleMaterials.get(particleId);
    if (material) {
      (material as any).dispose();
    }

    // 更新性能监视器
    const config = this.particleConfigs.get(particleId);
    if (config) {
      this.performanceMonitor.particleCount -= config.count;
    }

    // 移除引用
    this.particleGroups.delete(particleId);
    this.particleGeometries.delete(particleId);
    this.particleMaterials.delete(particleId);
    this.particleAttributes.delete(particleId);
    this.particleConfigs.delete(particleId);

    if (this.config.debug) {
      Debug.log('UnderwaterParticleSystem', `移除粒子组: ${particleId}`);
    }
  }

  /**
   * 移除水体相关的粒子
   * @param entityId 实体ID
   */
  private removeParticlesForWaterBody(entityId: string): void {
    // 查找与该水体相关的所有粒子组
    const particleIds = Array.from(this.particleGroups.keys())
      .filter(id => id.startsWith(`${entityId}_`));

    // 移除每个粒子组
    for (const particleId of particleIds) {
      const groupId = particleId.split('_')[1];
      this.removeParticleGroup(entityId, groupId);
    }

    if (this.config.debug) {
      Debug.log('UnderwaterParticleSystem', `移除水体相关的粒子: ${entityId}, 数量: ${particleIds.length}`);
    }
  }

  /**
   * 更新
   * @param deltaTime 时间增量
   */
  public update(deltaTime: number): void {
    if (!this.initialized || !this.config.enabled) {
      return;
    }

    // 开始性能监视
    const startTime = performance.now();

    // 更新时间
    this.time += deltaTime;

    // 更新所有粒子组
    for (const [particleId, particles] of this.particleGroups.entries()) {
      this.updateParticleGroup(particleId, particles, deltaTime);
    }

    // 结束性能监视
    const endTime = performance.now();
    this.performanceMonitor.renderTime = endTime - startTime;
    this.updatePerformanceMonitor(deltaTime);

    // 自动调整性能
    if (this.config.autoAdjustPerformance) {
      this.adjustPerformance();
    }
  }

  /**
   * 更新粒子组
   * @param particleId 粒子ID
   * @param particles 粒子对象
   * @param deltaTime 时间增量
   */
  private updateParticleGroup(particleId: string, particles: THREE.Points, deltaTime: number): void {
    const attributes = this.particleAttributes.get(particleId);
    if (!attributes) return;

    const config = this.particleConfigs.get(particleId);
    if (!config) return;

    const count = config.count;
    const {
      positions,
      sizes,
      colors,
      opacities,
      lifetimes,
      maxLifetimes,
      velocities,
      rotations,
      rotationSpeeds
    } = attributes;

    // 获取几何体属性
    const positionAttribute = particles.geometry.getAttribute('position') as THREE.BufferAttribute;
    const sizeAttribute = particles.geometry.getAttribute('size') as THREE.BufferAttribute;
    const colorAttribute = particles.geometry.getAttribute('color') as THREE.BufferAttribute;
    const opacityAttribute = particles.geometry.getAttribute('opacity') as THREE.BufferAttribute;

    // 获取水体组件
    const waterBody = this.getWaterBodyFromParticleId(particleId);

    // 更新粒子
    for (let i = 0; i < count; i++) {
      // 更新生命周期
      lifetimes[i] += deltaTime;
      if (lifetimes[i] >= maxLifetimes[i]) {
        // 重置粒子
        this.resetParticle(
          i,
          config,
          waterBody,
          positions,
          sizes,
          colors,
          opacities,
          lifetimes,
          maxLifetimes,
          velocities,
          rotations,
          rotationSpeeds
        );
      } else {
        // 更新位置
        positions[i * 3] += velocities[i * 3] * deltaTime;
        positions[i * 3 + 1] += velocities[i * 3 + 1] * deltaTime;
        positions[i * 3 + 2] += velocities[i * 3 + 2] * deltaTime;

        // 应用加速度
        if (config.acceleration) {
          velocities[i * 3] += config.acceleration.x * deltaTime;
          velocities[i * 3 + 1] += config.acceleration.y * deltaTime;
          velocities[i * 3 + 2] += config.acceleration.z * deltaTime;
        }

        // 更新旋转
        if (config.rotation) {
          rotations[i] += rotationSpeeds[i] * deltaTime;
        }

        // 更新透明度（基于生命周期）
        const lifeRatio = lifetimes[i] / maxLifetimes[i];
        if (lifeRatio > 0.8) {
          // 淡出
          opacities[i] = (1.0 - (lifeRatio - 0.8) / 0.2) * (config.opacity || 1.0);
        }
      }

      // 更新几何体属性
      positionAttribute.setXYZ(i, positions[i * 3], positions[i * 3 + 1], positions[i * 3 + 2]);
      sizeAttribute.setX(i, sizes[i]);
      colorAttribute.setXYZ(i, colors[i * 3], colors[i * 3 + 1], colors[i * 3 + 2]);
      opacityAttribute.setX(i, opacities[i]);
    }

    // 标记属性需要更新
    positionAttribute.needsUpdate = true;
    sizeAttribute.needsUpdate = true;
    colorAttribute.needsUpdate = true;
    opacityAttribute.needsUpdate = true;
  }

  /**
   * 重置粒子
   */
  private resetParticle(
    index: number,
    config: UnderwaterParticleConfig,
    waterBody: WaterBodyComponent | null,
    positions: Float32Array,
    sizes: Float32Array,
    colors: Float32Array,
    opacities: Float32Array,
    lifetimes: Float32Array,
    maxLifetimes: Float32Array,
    velocities: Float32Array,
    rotations: Float32Array,
    rotationSpeeds: Float32Array
  ): void {
    // 发射区域
    const emissionArea = config.emissionArea || {
      shape: 'box' as const,
      size: waterBody ? new THREE.Vector3(
        waterBody.getSize().width,
        waterBody.getSize().height,
        waterBody.getSize().depth
      ) : new THREE.Vector3(10, 10, 10),
      position: new THREE.Vector3(0, 0, 0)
    };

    // 重置位置
    const position = this.generateRandomPositionInArea(emissionArea);
    positions[index * 3] = position.x;
    positions[index * 3 + 1] = position.y;
    positions[index * 3 + 2] = position.z;

    // 重置大小
    sizes[index] = typeof config.size === 'number'
      ? config.size
      : Math.random() * (config.size[1] - config.size[0]) + config.size[0];

    // 重置透明度
    opacities[index] = config.opacity !== undefined ? config.opacity : 1.0;

    // 重置生命周期
    maxLifetimes[index] = typeof config.lifetime === 'number'
      ? config.lifetime
      : Math.random() * ((config.lifetime?.[1] || 5) - (config.lifetime?.[0] || 1)) + (config.lifetime?.[0] || 1);
    lifetimes[index] = 0;

    // 重置速度
    const speed = typeof config.speed === 'number'
      ? config.speed
      : Math.random() * ((config.speed?.[1] || 1) - (config.speed?.[0] || 0)) + (config.speed?.[0] || 0);
    const direction = new THREE.Vector3(
      Math.random() * 2 - 1,
      Math.random() * 2 - 1,
      Math.random() * 2 - 1
    ).normalize();
    velocities[index * 3] = direction.x * speed;
    velocities[index * 3 + 1] = direction.y * speed;
    velocities[index * 3 + 2] = direction.z * speed;

    // 重置旋转
    rotations[index] = Math.random() * Math.PI * 2;
    rotationSpeeds[index] = typeof config.rotationSpeed === 'number'
      ? config.rotationSpeed
      : Math.random() * ((config.rotationSpeed?.[1] || 0.1) - (config.rotationSpeed?.[0] || 0)) + (config.rotationSpeed?.[0] || 0);
  }

  /**
   * 更新性能监视器
   * @param deltaTime 时间增量
   */
  private updatePerformanceMonitor(deltaTime: number): void {
    // 计算帧率
    const now = performance.now();
    const frameTime = now - this.performanceMonitor.lastFrameTime;
    this.performanceMonitor.lastFrameTime = now;
    this.performanceMonitor.fps = 1000 / frameTime;

    if (this.config.debug && Math.floor(this.time) % 5 === 0) {
      Debug.log('UnderwaterParticleSystem', `性能: FPS=${this.performanceMonitor.fps.toFixed(1)}, 粒子数=${this.performanceMonitor.particleCount}, 渲染时间=${this.performanceMonitor.renderTime.toFixed(2)}ms`);
    }
  }

  /**
   * 自动调整性能
   */
  private adjustPerformance(): void {
    // 如果帧率过低，减少粒子数量
    if (this.performanceMonitor.fps < 30 && this.performanceMonitor.particleCount > 100) {
      // 找到粒子数量最多的组
      let maxCountParticleId = '';
      let maxCount = 0;
      for (const [particleId, config] of this.particleConfigs.entries()) {
        if (config.count > maxCount) {
          maxCount = config.count;
          maxCountParticleId = particleId;
        }
      }

      if (maxCountParticleId) {
        const config = this.particleConfigs.get(maxCountParticleId);
        if (config) {
          // 减少粒子数量
          const newCount = Math.max(Math.floor(config.count * 0.8), 10);
          if (newCount < config.count) {
            const entityId = maxCountParticleId.split('_')[0];
            const groupId = maxCountParticleId.split('_')[1];

            // 创建新配置
            const newConfig = { ...config, count: newCount };

            // 重新创建粒子组
            this.removeParticleGroup(entityId, groupId);
            this.addParticleGroup(entityId, groupId, newConfig);

            if (this.config.debug) {
              Debug.log('UnderwaterParticleSystem', `自动调整性能: 减少粒子组 ${maxCountParticleId} 的数量从 ${config.count} 到 ${newCount}`);
            }
          }
        }
      }
    }
  }
}
