/**
 * 测试水下粒子系统文件的语法
 */
const fs = require('fs');
const path = require('path');

console.log('开始检查 UnderwaterParticleSystem.ts 文件...');

try {
  // 读取文件
  const filePath = path.join(__dirname, 'src/rendering/water/UnderwaterParticleSystem.ts');
  const content = fs.readFileSync(filePath, 'utf8');
  
  console.log(`✓ 文件读取成功`);
  console.log(`  - 文件大小: ${content.length} 字符`);
  console.log(`  - 行数: ${content.split('\n').length}`);
  
  // 检查基本语法结构
  const lines = content.split('\n');
  let braceCount = 0;
  let parenCount = 0;
  let bracketCount = 0;
  let inString = false;
  let stringChar = '';
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    for (let j = 0; j < line.length; j++) {
      const char = line[j];
      const prevChar = j > 0 ? line[j - 1] : '';
      
      // 处理字符串
      if ((char === '"' || char === "'" || char === '`') && prevChar !== '\\') {
        if (!inString) {
          inString = true;
          stringChar = char;
        } else if (char === stringChar) {
          inString = false;
          stringChar = '';
        }
      }
      
      // 如果在字符串中，跳过括号计数
      if (inString) continue;
      
      // 计数括号
      switch (char) {
        case '{': braceCount++; break;
        case '}': braceCount--; break;
        case '(': parenCount++; break;
        case ')': parenCount--; break;
        case '[': bracketCount++; break;
        case ']': bracketCount--; break;
      }
    }
  }
  
  console.log(`✓ 语法结构检查完成`);
  console.log(`  - 大括号平衡: ${braceCount === 0 ? '✓' : '✗ (' + braceCount + ')'}`);
  console.log(`  - 小括号平衡: ${parenCount === 0 ? '✓' : '✗ (' + parenCount + ')'}`);
  console.log(`  - 方括号平衡: ${bracketCount === 0 ? '✓' : '✗ (' + bracketCount + ')'}`);
  
  // 检查导入语句
  const imports = lines.filter(line => line.trim().startsWith('import'));
  console.log(`✓ 导入语句检查: 找到 ${imports.length} 个导入`);
  imports.forEach(imp => console.log(`  - ${imp.trim()}`));
  
  // 检查导出语句
  const exports = lines.filter(line => line.trim().startsWith('export'));
  console.log(`✓ 导出语句检查: 找到 ${exports.length} 个导出`);
  
  // 检查类定义
  const classLines = lines.filter(line => line.includes('class ') && line.includes('extends'));
  console.log(`✓ 类定义检查: 找到 ${classLines.length} 个类`);
  classLines.forEach(cls => console.log(`  - ${cls.trim()}`));
  
  // 检查接口定义
  const interfaceLines = lines.filter(line => line.includes('interface '));
  console.log(`✓ 接口定义检查: 找到 ${interfaceLines.length} 个接口`);
  
  // 检查枚举定义
  const enumLines = lines.filter(line => line.includes('enum '));
  console.log(`✓ 枚举定义检查: 找到 ${enumLines.length} 个枚举`);
  
  console.log('\n🎉 文件检查完成，未发现明显的语法错误！');
  
} catch (error) {
  console.error('❌ 检查过程中发生错误:', error.message);
  process.exit(1);
}
